'use client';
import React, { useState, useEffect, useContext, useCallback } from 'react';
import { Box, Typography } from '@mui/material';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import NoDataView from '@/components/UI/NoDataView';
import DialogBox from '@/components/UI/Modalbox';
import CommonUserDetails from '@/components/UI/CommonUserDetails';
import ContentLoader from '@/components/UI/ContentLoader';
import AuthContext from '@/helper/authcontext';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import Icon from '@/components/UI/AppIcon/AppIcon';

const filterFields = [
  {
    type: 'text',
    label: 'Search',
    name: 'search',
    placeholder: 'Search',
  },
  {
    type: 'date-range',
    label: 'Date Range',
    name: 'dateRange',
    placeholder: 'Select date range',
    format: 'MMM dd, yyyy',
  },
];

export default function ActivityReports() {
  const { authState, setUserdata } = useContext(AuthContext);

  const [activityList, setActivityList] = useState([]);
  const [loader, setLoader] = useState(false);
  const [filters, setFilters] = useState({});
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [toggleModal, setToggleModal] = useState(false);
  const [userAgentValue, setUserAgentValue] = useState('');

  // Menu items for action dropdown
  const menuItems = [
    {
      label: 'User Agent',
      icon: <Icon name="Eye" size={16} />,
      onClick: (_, rowData) => {
        setToggleModal(!toggleModal);
        setUserAgentValue({
          userAgent: rowData?.userAgent,
        });
      },
    },
  ];

  // CommonTable columns (keeping same structure)
  const columns = [
    {
      header: 'ID',
      accessor: 'user_id',
      sortable: false,
    },
    {
      header: 'Name',
      accessor: 'user_full_name',
      sortable: false,
      renderCell: (_, row) => (
        <CommonUserDetails
          userData={row?.users}
          searchValue={searchValue}
          page={page}
          rowsPerPage={rowsPerPage}
          authState={authState}
          setUserdata={setUserdata}
        />
      ),
    },
    {
      header: 'Date & Time',
      accessor: 'createdAt',
      sortable: false,
      renderCell: (value) => DateFormat(value, 'datesWithhour'),
    },
    {
      header: 'Activity actions',
      accessor: 'activity_action',
      sortable: false,
      renderCell: (_, row) =>
        `${row?.activity_table ? row?.activity_table : ''} ${row?.activity_action ? row?.activity_action : ''}`.trim(),
    },
    {
      header: 'IP',
      accessor: 'ip_address',
      sortable: false,
      renderCell: (value) => (value ? value : '-'),
    },
    {
      header: 'Location',
      accessor: 'location',
      sortable: false,
      renderCell: (value) => (value ? value : '-'),
    },
    {
      header: 'Address',
      accessor: 'address',
      sortable: false,
      renderCell: (value) => (value ? value : '-'),
    },
    // User agent column is now handled by CommonTable's actionMenuItems prop
  ];

  // Get activity details from API (same as Activity Logs)
  const getActivityDetails = useCallback(
    async (
      search = '',
      pageNo = 1,
      Rpp = rowsPerPage,
      startDate = '',
      endDate = ''
    ) => {
      setLoader(true);
      try {
        // Build query parameters
        let queryParams = `?search=${search}&page=${pageNo}&size=${Rpp}`;
        if (startDate && endDate) {
          queryParams += `&start_date=${startDate}&end_date=${endDate}`;
        }

        const { status, data } = await axiosInstance.get(
          URLS?.ACTIVITY_LOGS + queryParams
        );

        if (status === 200) {
          const activityData =
            data?.data &&
            data?.data?.length > 0 &&
            data?.data?.map((a, index) => {
              const newdata = JSON.parse(a?.new_data);
              return {
                ...a,
                user_id: a?.users?.id,
                id: index,
                user_full_name: a?.users?.user_full_name,
                user_email: a?.users?.user_email,
                branch_name: newdata?.branch_name ? newdata?.branch_name : '-',
                ip_address: a?.ip_address ? a?.ip_address : '-',
                new_data: newdata,
                department_name: newdata?.department_name
                  ? newdata?.department_name
                  : '-',
              };
            });
          setPage(data?.page);
          setTotalCount(data?.count);
          setActivityList(activityData ? activityData : []);
          setLoader(false);
        }
      } catch (error) {
        setLoader(false);
        setActivityList([]);
        setApiMessage('error', error?.response?.data?.message);
      }
    },
    [rowsPerPage]
  );

  // Handle filter apply
  const handleApplyFilters = (values) => {
    setFilters(values);
    setSearchValue(values.search || '');
    const searchTerm = values.search || '';

    // Extract date range values - CustomDateRangePicker returns [startDate, endDate] array
    const startDate = values.dateRange?.[0]
      ? new Date(values.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = values.dateRange?.[1]
      ? new Date(values.dateRange[1]).toISOString().split('T')[0]
      : '';

    setPage(1);
    getActivityDetails(searchTerm, 1, rowsPerPage, startDate, endDate);
  };

  // Handle pagination (Rota Reports style)
  const handlePageChange = (newPage) => {
    setPage(newPage);
    const searchTerm = filters.search || '';
    const startDate = filters.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';
    getActivityDetails(searchTerm, newPage, rowsPerPage, startDate, endDate);
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
    const searchTerm = filters.search || '';
    const startDate = filters.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';
    getActivityDetails(searchTerm, 1, newRowsPerPage, startDate, endDate);
  };

  // Initial load
  useEffect(() => {
    getActivityDetails();
  }, [getActivityDetails]);

  return (
    <>
      <Box className="report-main-container">
        <FilterCollapse
          fields={filterFields}
          onApply={handleApplyFilters}
          buttonText="Apply Filters"
          initialValues={filters}
        />

        <Box className="report-table-container">
          {loader ? (
            <ContentLoader />
          ) : activityList && activityList?.length === 0 ? (
            <NoDataView
              title="No Activity Records Found"
              description="There is no Activity data available at the moment."
            />
          ) : (
            <CommonTable
              columns={columns}
              data={activityList}
              pageSize={rowsPerPage}
              currentPage={page}
              totalCount={totalCount}
              onPageChange={handlePageChange}
              onRowsPerPageChange={handleRowsPerPageChange}
              actionMenuItems={menuItems}
            />
          )}
        </Box>
      </Box>
      <DialogBox
        open={toggleModal}
        handleClose={() => {
          setToggleModal(!toggleModal);
          setUserAgentValue('');
        }}
        title="User Agent"
        className="dialog-box-container"
        content={
          <Box>
            <Typography>{userAgentValue?.userAgent}</Typography>
          </Box>
        }
      />
    </>
  );
}
